const fs = require('fs');
const path = require('path');

/**
 * 全面扫描视觉脚本节点，特别关注学习记录、RAG应用和数字人制作系统
 */

// 节点分类映射
const nodeCategories = {
  // 学习记录系统相关
  learning: ['learning', 'xapi', 'education', 'achievement', 'progress'],
  // RAG应用系统相关
  rag: ['rag', 'knowledge', 'semantic', 'document', 'retrieval', 'embedding'],
  // 数字人制作系统相关
  digitalHuman: ['avatar', 'digital', 'human', 'character', 'face', 'expression', 'virtual'],
  // AI相关
  ai: ['ai', 'ml', 'deep', 'neural', 'model', 'inference'],
  // 其他分类
  rendering: ['render', 'material', 'shader', 'light', 'camera'],
  physics: ['physics', 'collision', 'rigid', 'soft'],
  animation: ['animation', 'tween', 'keyframe'],
  audio: ['audio', 'sound', 'music', 'voice'],
  input: ['input', 'keyboard', 'mouse', 'touch', 'gesture'],
  network: ['network', 'websocket', 'http', 'sync'],
  ui: ['ui', 'interface', 'element', 'layout'],
  scene: ['scene', 'object', 'entity', 'transform'],
  spatial: ['spatial', 'gis', 'coordinate', 'location'],
  blockchain: ['blockchain', 'wallet', 'contract', 'transaction'],
  collaboration: ['collaboration', 'sync', 'presence', 'version'],
  industrial: ['industrial', 'mes', 'device', 'maintenance'],
  edge: ['edge', 'device', '5g', 'routing'],
  vr: ['vr', 'ar', 'spatial', 'tracking', 'controller'],
  game: ['game', 'player', 'inventory', 'quest'],
  social: ['social', 'friend', 'chat', 'community'],
  payment: ['payment', 'subscription', 'wallet', 'transaction'],
  mocap: ['mocap', 'motion', 'capture', 'tracking', 'pose'],
  terrain: ['terrain', 'height', 'sculpt', 'paint'],
  particle: ['particle', 'emitter', 'effect'],
  water: ['water', 'fluid', 'wave'],
  material: ['material', 'texture', 'pbr']
};

/**
 * 分析节点文件
 */
function analyzeNodeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath, '.ts');
    
    // 查找类定义
    const classRegex = /export\s+class\s+(\w+Node)\s+extends\s+/g;
    const nodes = [];
    let match;
    
    while ((match = classRegex.exec(content)) !== null) {
      nodes.push(match[1]);
    }
    
    // 分析节点类型
    const category = categorizeNode(fileName, content);
    
    return {
      fileName,
      filePath,
      nodeClasses: nodes,
      nodeCount: nodes.length,
      category,
      content: content.substring(0, 500) // 保存部分内容用于分析
    };
  } catch (error) {
    return {
      fileName: path.basename(filePath, '.ts'),
      filePath,
      nodeClasses: [],
      nodeCount: 0,
      category: 'unknown',
      error: error.message
    };
  }
}

/**
 * 节点分类
 */
function categorizeNode(fileName, content) {
  const lowerFileName = fileName.toLowerCase();
  const lowerContent = content.toLowerCase();
  
  for (const [category, keywords] of Object.entries(nodeCategories)) {
    for (const keyword of keywords) {
      if (lowerFileName.includes(keyword) || lowerContent.includes(keyword)) {
        return category;
      }
    }
  }
  
  return 'other';
}

/**
 * 扫描目录
 */
function scanDirectory(dirPath) {
  const results = [];
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // 递归扫描子目录
        results.push(...scanDirectory(itemPath));
      } else if (item.endsWith('.ts') && !item.endsWith('.test.ts') && !item.endsWith('.spec.ts')) {
        // 分析TypeScript文件
        const analysis = analyzeNodeFile(itemPath);
        if (analysis.nodeCount > 0) {
          results.push(analysis);
        }
      }
    }
  } catch (error) {
    console.error(`扫描目录失败 ${dirPath}:`, error.message);
  }
  
  return results;
}

/**
 * 生成详细报告
 */
function generateDetailedReport(results) {
  const categoryStats = {};
  const specialNodes = {
    learning: [],
    rag: [],
    digitalHuman: [],
    ai: []
  };
  
  let totalNodes = 0;
  
  // 统计分析
  for (const result of results) {
    totalNodes += result.nodeCount;
    
    if (!categoryStats[result.category]) {
      categoryStats[result.category] = { count: 0, files: [] };
    }
    categoryStats[result.category].count += result.nodeCount;
    categoryStats[result.category].files.push(result.fileName);
    
    // 收集特殊节点
    if (result.category === 'learning') {
      specialNodes.learning.push(...result.nodeClasses);
    } else if (result.category === 'rag') {
      specialNodes.rag.push(...result.nodeClasses);
    } else if (result.category === 'digitalHuman') {
      specialNodes.digitalHuman.push(...result.nodeClasses);
    } else if (result.category === 'ai') {
      specialNodes.ai.push(...result.nodeClasses);
    }
  }
  
  console.log('🔍 全面节点扫描报告\n');
  console.log('=' .repeat(60));
  console.log(`\n📊 总节点数量: ${totalNodes}个`);
  console.log(`📁 节点文件数量: ${results.length}个\n`);
  
  // 特殊关注的系统
  console.log('🎯 重点关注系统分析:\n');
  
  console.log('📚 学习记录系统节点:');
  if (specialNodes.learning.length > 0) {
    console.log(`   数量: ${specialNodes.learning.length}个`);
    console.log(`   节点: ${specialNodes.learning.join(', ')}`);
  } else {
    console.log('   ❌ 未发现学习记录系统节点');
  }
  
  console.log('\n🔍 RAG应用系统节点:');
  if (specialNodes.rag.length > 0) {
    console.log(`   数量: ${specialNodes.rag.length}个`);
    console.log(`   节点: ${specialNodes.rag.join(', ')}`);
  } else {
    console.log('   ❌ 未发现RAG应用系统节点');
  }
  
  console.log('\n👤 数字人制作系统节点:');
  if (specialNodes.digitalHuman.length > 0) {
    console.log(`   数量: ${specialNodes.digitalHuman.length}个`);
    console.log(`   节点: ${specialNodes.digitalHuman.join(', ')}`);
  } else {
    console.log('   ❌ 未发现数字人制作系统节点');
  }
  
  console.log('\n🤖 AI系统节点:');
  if (specialNodes.ai.length > 0) {
    console.log(`   数量: ${specialNodes.ai.length}个`);
    console.log(`   节点: ${specialNodes.ai.slice(0, 10).join(', ')}${specialNodes.ai.length > 10 ? '...' : ''}`);
  } else {
    console.log('   ❌ 未发现AI系统节点');
  }
  
  // 分类统计
  console.log('\n📋 按分类统计:');
  const sortedCategories = Object.entries(categoryStats)
    .sort((a, b) => b[1].count - a[1].count);
  
  for (const [category, data] of sortedCategories) {
    console.log(`   ${category}: ${data.count}个节点 (${data.files.length}个文件)`);
  }
  
  return {
    totalNodes,
    categoryStats,
    specialNodes,
    results
  };
}

/**
 * 查找特定节点
 */
function findSpecificNodes(results) {
  const specificFindings = {
    learningRecord: [],
    ragApplication: [],
    digitalHuman: [],
    xapi: [],
    knowledgeBase: [],
    avatar: []
  };
  
  for (const result of results) {
    const content = result.content?.toLowerCase() || '';
    const fileName = result.fileName.toLowerCase();
    
    // 学习记录相关
    if (fileName.includes('learning') || content.includes('learning') || 
        fileName.includes('xapi') || content.includes('xapi')) {
      specificFindings.learningRecord.push({
        file: result.fileName,
        nodes: result.nodeClasses,
        path: result.filePath
      });
    }
    
    // RAG应用相关
    if (fileName.includes('rag') || content.includes('rag') ||
        fileName.includes('knowledge') || content.includes('knowledge') ||
        fileName.includes('semantic') || content.includes('semantic')) {
      specificFindings.ragApplication.push({
        file: result.fileName,
        nodes: result.nodeClasses,
        path: result.filePath
      });
    }
    
    // 数字人相关
    if (fileName.includes('avatar') || content.includes('avatar') ||
        fileName.includes('digital') || content.includes('digital') ||
        fileName.includes('human') || content.includes('human') ||
        fileName.includes('character') || content.includes('character')) {
      specificFindings.digitalHuman.push({
        file: result.fileName,
        nodes: result.nodeClasses,
        path: result.filePath
      });
    }
  }
  
  return specificFindings;
}

// 执行扫描
if (require.main === module) {
  console.log('🚀 开始全面扫描视觉脚本节点...\n');
  
  const engineNodesPath = path.resolve('engine/src/visual-script/nodes');
  const results = scanDirectory(engineNodesPath);
  
  const report = generateDetailedReport(results);
  const specificFindings = findSpecificNodes(results);
  
  console.log('\n🔍 特定节点查找结果:\n');
  
  console.log('📚 学习记录相关文件:');
  specificFindings.learningRecord.forEach(item => {
    console.log(`   ${item.file}: ${item.nodes.join(', ')}`);
  });
  
  console.log('\n🔍 RAG应用相关文件:');
  specificFindings.ragApplication.forEach(item => {
    console.log(`   ${item.file}: ${item.nodes.join(', ')}`);
  });
  
  console.log('\n👤 数字人相关文件:');
  specificFindings.digitalHuman.forEach(item => {
    console.log(`   ${item.file}: ${item.nodes.join(', ')}`);
  });
  
  console.log('\n✅ 扫描完成！');
  console.log(`\n📊 最终统计: ${report.totalNodes}个节点，${results.length}个文件`);
}

module.exports = {
  analyzeNodeFile,
  scanDirectory,
  generateDetailedReport,
  findSpecificNodes
};
