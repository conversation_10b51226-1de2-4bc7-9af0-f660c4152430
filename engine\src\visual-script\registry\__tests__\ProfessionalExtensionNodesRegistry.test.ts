/**
 * 专业应用与扩展节点注册表测试
 */

import { ProfessionalExtensionNodesRegistry, professionalExtensionNodesRegistry } from '../ProfessionalExtensionNodesRegistry';
import { NodeRegistry } from '../NodeRegistry';

// Mock NodeRegistry
jest.mock('../NodeRegistry');

describe('ProfessionalExtensionNodesRegistry', () => {
  let registry: ProfessionalExtensionNodesRegistry;
  let mockNodeRegistry: jest.Mocked<NodeRegistry>;

  beforeEach(() => {
    // 重置单例实例
    (ProfessionalExtensionNodesRegistry as any).instance = undefined;
    registry = ProfessionalExtensionNodesRegistry.getInstance();
    
    // 获取模拟的NodeRegistry实例
    mockNodeRegistry = NodeRegistry.getInstance() as jest.Mocked<NodeRegistry>;
    mockNodeRegistry.registerNode = jest.fn();
    
    // 重置注册状态
    registry.resetRegistration();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('单例模式', () => {
    test('应该返回同一个实例', () => {
      const instance1 = ProfessionalExtensionNodesRegistry.getInstance();
      const instance2 = ProfessionalExtensionNodesRegistry.getInstance();
      expect(instance1).toBe(instance2);
    });

    test('导出的实例应该是单例实例', () => {
      const instance = ProfessionalExtensionNodesRegistry.getInstance();
      expect(professionalExtensionNodesRegistry).toBe(instance);
    });
  });

  describe('节点注册', () => {
    test('应该成功注册所有33个节点', async () => {
      await registry.registerAllNodes();

      // 验证registerNode被调用了33次
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledTimes(33);
      
      // 验证注册状态
      expect(registry.isRegistered()).toBe(true);
      
      // 验证统计信息
      const stats = registry.getRegistrationStats();
      expect(stats.total).toBe(33);
      expect(stats.registered).toBe(33);
      expect(stats.failed).toBe(0);
    });

    test('应该防止重复注册', async () => {
      await registry.registerAllNodes();
      await registry.registerAllNodes(); // 第二次调用

      // 应该只调用一次
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledTimes(33);
    });

    test('应该正确注册批次34扩展节点', async () => {
      await registry.registerAllNodes();

      // 验证支付系统节点注册
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'PaymentGateway',
        expect.any(Function),
        '批次34扩展',
        '处理支付网关集成',
        'extension',
        '#9C27B0'
      );

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'Subscription',
        expect.any(Function),
        '批次34扩展',
        '管理用户订阅',
        'extension',
        '#9C27B0'
      );

      // 验证第三方集成节点注册
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'GoogleServices',
        expect.any(Function),
        '批次34扩展',
        '集成Google服务',
        'extension',
        '#9C27B0'
      );
    });

    test('应该正确注册UI系统节点', async () => {
      await registry.registerAllNodes();

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'CreateUIElement',
        expect.any(Function),
        'UI系统',
        '创建各种UI元素',
        'layout',
        '#2196F3'
      );

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'UILayout',
        expect.any(Function),
        'UI系统',
        '管理UI布局',
        'layout',
        '#2196F3'
      );
    });

    test('应该正确注册动作捕捉节点', async () => {
      await registry.registerAllNodes();

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'FaceDetection',
        expect.any(Function),
        '动作捕捉',
        '检测面部特征',
        'camera',
        '#FF5722'
      );

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'PoseDetection',
        expect.any(Function),
        '动作捕捉',
        '检测身体姿态',
        'camera',
        '#FF5722'
      );
    });

    test('应该正确注册空间信息节点', async () => {
      await registry.registerAllNodes();

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'GISAnalysis',
        expect.any(Function),
        '空间信息',
        '执行GIS空间分析',
        'map',
        '#4CAF50'
      );
    });

    test('应该正确注册粒子系统节点', async () => {
      await registry.registerAllNodes();

      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'ParticleEmitter',
        expect.any(Function),
        '粒子系统',
        '创建和控制粒子发射器',
        'grain',
        '#E91E63'
      );
    });

    test('应该正确注册专业节点', async () => {
      await registry.registerAllNodes();

      // 区块链节点
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'WalletConnect',
        expect.any(Function),
        '区块链',
        '连接区块链钱包',
        'extension',
        '#FF9800'
      );

      // 学习记录节点
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'LearningRecord',
        expect.any(Function),
        '学习记录',
        '创建和管理学习记录',
        'extension',
        '#3F51B5'
      );

      // RAG应用节点
      expect(mockNodeRegistry.registerNode).toHaveBeenCalledWith(
        'RAGQuery',
        expect.any(Function),
        'RAG应用',
        '执行RAG查询',
        'extension',
        '#009688'
      );
    });
  });

  describe('统计信息', () => {
    test('应该返回正确的注册统计信息', async () => {
      await registry.registerAllNodes();
      
      const stats = registry.getRegistrationStats();
      expect(stats.total).toBe(33);
      expect(stats.registered).toBe(33);
      expect(stats.failed).toBe(0);
      expect(stats.successRate).toBe('100.00%');
      expect(stats.categories.batch34Extension).toBe(9);
      expect(stats.categories.uiSystem).toBe(3);
      expect(stats.categories.motionCapture).toBe(3);
      expect(stats.categories.spatialInfo).toBe(3);
      expect(stats.categories.particleSystem).toBe(2);
      expect(stats.categories.professional).toBe(13);
    });

    test('应该返回正确的节点分类信息', () => {
      const categories = registry.getNodeCategories();
      
      expect(categories['批次34扩展'].count).toBe(9);
      expect(categories['UI系统'].count).toBe(3);
      expect(categories['动作捕捉'].count).toBe(3);
      expect(categories['空间信息'].count).toBe(3);
      expect(categories['粒子系统'].count).toBe(2);
      expect(categories['区块链'].count).toBe(3);
      expect(categories['学习记录'].count).toBe(3);
      expect(categories['RAG应用'].count).toBe(4);
      expect(categories['监控服务'].count).toBe(2);
      expect(categories['通知服务'].count).toBe(1);
    });
  });

  describe('节点类型管理', () => {
    test('应该返回所有已注册的节点类型', () => {
      const nodeTypes = registry.getAllRegisteredNodeTypes();

      expect(nodeTypes).toHaveLength(33);
      
      // 验证批次34扩展节点
      expect(nodeTypes).toContain('PaymentGateway');
      expect(nodeTypes).toContain('Subscription');
      expect(nodeTypes).toContain('GoogleServices');
      
      // 验证UI系统节点
      expect(nodeTypes).toContain('CreateUIElement');
      expect(nodeTypes).toContain('UILayout');
      
      // 验证动作捕捉节点
      expect(nodeTypes).toContain('FaceDetection');
      expect(nodeTypes).toContain('PoseDetection');
      
      // 验证空间信息节点
      expect(nodeTypes).toContain('GISAnalysis');
      
      // 验证粒子系统节点
      expect(nodeTypes).toContain('ParticleEmitter');
      
      // 验证专业节点
      expect(nodeTypes).toContain('WalletConnect');
      expect(nodeTypes).toContain('LearningRecord');
      expect(nodeTypes).toContain('RAGQuery');
    });
  });

  describe('错误处理', () => {
    test('应该处理注册失败的情况', async () => {
      // 模拟注册失败
      mockNodeRegistry.registerNode.mockImplementation(() => {
        throw new Error('注册失败');
      });

      await expect(registry.registerAllNodes()).rejects.toThrow();
    });
  });

  describe('重置功能', () => {
    test('应该正确重置注册状态', async () => {
      await registry.registerAllNodes();
      expect(registry.isRegistered()).toBe(true);

      registry.resetRegistration();
      expect(registry.isRegistered()).toBe(false);
      
      const stats = registry.getRegistrationStats();
      expect(stats.registered).toBe(0);
      expect(stats.failed).toBe(0);
    });
  });
});
