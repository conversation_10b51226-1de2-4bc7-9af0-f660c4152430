/**
 * 批次15：专业应用与扩展节点注册表
 * 注册33个专业应用与扩展节点到编辑器
 */

import { NodeRegistry } from './NodeRegistry';

// 导入批次34扩展节点 (9个)
import {
  PaymentGatewayNode,
  SubscriptionNode,
  InAppPurchaseNode,
  WalletSystemNode,
  TransactionHistoryNode,
  PaymentAnalyticsNode
} from '../nodes/batch34/PaymentNodes';

import {
  GoogleServicesNode,
  FacebookIntegrationNode,
  TwitterIntegrationNode
} from '../nodes/batch34/ThirdPartyNodes';

// 导入UI系统节点 (3个)
import {
  CreateUIElementNode,
  UILayoutNode,
  UIEventHandlerNode
} from '../nodes/ui/UINodes';

// 导入动作捕捉节点 (3个)
import {
  FaceDetectionNode
} from '../nodes/mocap/FaceDetectionNode';

import {
  PoseDetectionNode,
  VirtualInteractionNode
} from '../nodes/mocap/MotionCaptureNodes';

// 导入空间信息节点 (3个)
import {
  GISAnalysisNode,
  GeospatialVisualizationNode,
  LocationServicesNode
} from '../nodes/spatial/SpatialInformationNodes';

// 导入粒子系统节点 (2个)
import {
  ParticleEmitterNode,
  ParticleEffectNode
} from '../nodes/particles/ParticleSystemNodes';

// 导入其他专业节点 (13个)
// 区块链节点
import {
  WalletConnectNode,
  SmartContractNode,
  TransactionNode
} from '../nodes/blockchain/BlockchainNodes';

// 学习记录节点
import {
  LearningRecordNode,
  LearningPathNode,
  KnowledgeGraphNode
} from '../nodes/learning/LearningRecordNodes';

// RAG应用节点
import {
  RAGQueryNode,
  KnowledgeBaseNode,
  DocumentIndexNode,
  SemanticSearchNode
} from '../nodes/rag/RAGApplicationNodes';

// 监控服务节点
import {
  SystemMonitoringNode,
  PerformanceAnalysisNode
} from '../nodes/monitoring/MonitoringServiceNodes';

// 通知服务节点
import {
  EmailNotificationNode
} from '../nodes/notification/NotificationServiceNodes';

/**
 * 专业应用与扩展节点注册表类
 */
export class ProfessionalExtensionNodesRegistry {
  private static instance: ProfessionalExtensionNodesRegistry;
  private nodeRegistry: NodeRegistry;
  private registered = false;
  private registrationStats = {
    total: 33,
    registered: 0,
    failed: 0,
    categories: {
      batch34Extension: 9,
      uiSystem: 3,
      motionCapture: 3,
      spatialInfo: 3,
      particleSystem: 2,
      professional: 13
    }
  };

  private constructor() {
    this.nodeRegistry = NodeRegistry.getInstance();
  }

  public static getInstance(): ProfessionalExtensionNodesRegistry {
    if (!ProfessionalExtensionNodesRegistry.instance) {
      ProfessionalExtensionNodesRegistry.instance = new ProfessionalExtensionNodesRegistry();
    }
    return ProfessionalExtensionNodesRegistry.instance;
  }

  /**
   * 注册所有专业应用与扩展节点
   */
  public async registerAllNodes(): Promise<void> {
    if (this.registered) {
      console.log('专业应用与扩展节点已经注册过了');
      return;
    }

    console.log('开始注册批次15：专业应用与扩展节点...');

    try {
      // 注册批次34扩展节点 (9个)
      await this.registerBatch34ExtensionNodes();

      // 注册UI系统节点 (3个)
      await this.registerUISystemNodes();

      // 注册动作捕捉节点 (3个)
      await this.registerMotionCaptureNodes();

      // 注册空间信息节点 (3个)
      await this.registerSpatialInfoNodes();

      // 注册粒子系统节点 (2个)
      await this.registerParticleSystemNodes();

      // 注册其他专业节点 (13个)
      await this.registerProfessionalNodes();

      this.registered = true;
      console.log(`批次15专业应用与扩展节点注册完成！总计${this.registrationStats.registered}个节点`);

    } catch (error) {
      console.error('专业应用与扩展节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册批次34扩展节点 (9个)
   */
  private async registerBatch34ExtensionNodes(): Promise<void> {
    console.log('注册批次34扩展节点...');

    const batch34Nodes = [
      // 支付系统节点 (6个)
      { nodeClass: PaymentGatewayNode, type: 'PaymentGateway', name: '支付网关', desc: '处理支付网关集成' },
      { nodeClass: SubscriptionNode, type: 'Subscription', name: '订阅管理', desc: '管理用户订阅' },
      { nodeClass: InAppPurchaseNode, type: 'InAppPurchase', name: '应用内购买', desc: '处理应用内购买' },
      { nodeClass: WalletSystemNode, type: 'WalletSystem', name: '钱包系统', desc: '管理用户钱包' },
      { nodeClass: TransactionHistoryNode, type: 'TransactionHistory', name: '交易历史', desc: '查看交易历史' },
      { nodeClass: PaymentAnalyticsNode, type: 'PaymentAnalytics', name: '支付分析', desc: '支付数据分析' },
      
      // 第三方集成节点 (3个)
      { nodeClass: GoogleServicesNode, type: 'GoogleServices', name: 'Google服务', desc: '集成Google服务' },
      { nodeClass: FacebookIntegrationNode, type: 'FacebookIntegration', name: 'Facebook集成', desc: '集成Facebook功能' },
      { nodeClass: TwitterIntegrationNode, type: 'TwitterIntegration', name: 'Twitter集成', desc: '集成Twitter功能' }
    ];

    for (const { nodeClass, type, name, desc } of batch34Nodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          '批次34扩展',
          desc,
          'extension',
          '#9C27B0'
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('批次34扩展节点注册完成 - 9个节点');
  }

  /**
   * 注册UI系统节点 (3个)
   */
  private async registerUISystemNodes(): Promise<void> {
    console.log('注册UI系统节点...');

    const uiNodes = [
      { nodeClass: CreateUIElementNode, type: 'CreateUIElement', name: '创建UI元素', desc: '创建各种UI元素' },
      { nodeClass: UILayoutNode, type: 'UILayout', name: 'UI布局', desc: '管理UI布局' },
      { nodeClass: UIEventHandlerNode, type: 'UIEventHandler', name: 'UI事件处理', desc: '处理UI事件' }
    ];

    for (const { nodeClass, type, name, desc } of uiNodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          'UI系统',
          desc,
          'layout',
          '#2196F3'
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('UI系统节点注册完成 - 3个节点');
  }

  /**
   * 注册动作捕捉节点 (3个)
   */
  private async registerMotionCaptureNodes(): Promise<void> {
    console.log('注册动作捕捉节点...');

    const motionCaptureNodes = [
      { nodeClass: FaceDetectionNode, type: 'FaceDetection', name: '面部检测', desc: '检测面部特征' },
      { nodeClass: PoseDetectionNode, type: 'PoseDetection', name: '姿态检测', desc: '检测身体姿态' },
      { nodeClass: VirtualInteractionNode, type: 'VirtualInteraction', name: '虚拟交互', desc: '虚拟环境交互' }
    ];

    for (const { nodeClass, type, name, desc } of motionCaptureNodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          '动作捕捉',
          desc,
          'camera',
          '#FF5722'
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('动作捕捉节点注册完成 - 3个节点');
  }

  /**
   * 注册空间信息节点 (3个)
   */
  private async registerSpatialInfoNodes(): Promise<void> {
    console.log('注册空间信息节点...');

    const spatialNodes = [
      { nodeClass: GISAnalysisNode, type: 'GISAnalysis', name: 'GIS分析', desc: '执行GIS空间分析' },
      { nodeClass: GeospatialVisualizationNode, type: 'GeospatialVisualization', name: '地理空间可视化', desc: '地理空间数据可视化' },
      { nodeClass: LocationServicesNode, type: 'LocationServices', name: '位置服务', desc: '提供位置相关服务' }
    ];

    for (const { nodeClass, type, name, desc } of spatialNodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          '空间信息',
          desc,
          'map',
          '#4CAF50'
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('空间信息节点注册完成 - 3个节点');
  }

  /**
   * 注册粒子系统节点 (2个)
   */
  private async registerParticleSystemNodes(): Promise<void> {
    console.log('注册粒子系统节点...');

    const particleNodes = [
      { nodeClass: ParticleEmitterNode, type: 'ParticleEmitter', name: '粒子发射器', desc: '创建和控制粒子发射器' },
      { nodeClass: ParticleEffectNode, type: 'ParticleEffect', name: '粒子效果', desc: '创建粒子效果' }
    ];

    for (const { nodeClass, type, name, desc } of particleNodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          '粒子系统',
          desc,
          'grain',
          '#E91E63'
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('粒子系统节点注册完成 - 2个节点');
  }

  /**
   * 注册其他专业节点 (13个)
   */
  private async registerProfessionalNodes(): Promise<void> {
    console.log('注册其他专业节点...');

    // 区块链节点 (3个)
    const blockchainNodes = [
      { nodeClass: WalletConnectNode, type: 'WalletConnect', name: '钱包连接', desc: '连接区块链钱包' },
      { nodeClass: SmartContractNode, type: 'SmartContract', name: '智能合约', desc: '执行智能合约' },
      { nodeClass: TransactionNode, type: 'Transaction', name: '区块链交易', desc: '处理区块链交易' }
    ];

    // 学习记录节点 (3个)
    const learningNodes = [
      { nodeClass: LearningRecordNode, type: 'LearningRecord', name: '学习记录', desc: '创建和管理学习记录' },
      { nodeClass: LearningPathNode, type: 'LearningPath', name: '学习路径', desc: '管理学习路径' },
      { nodeClass: KnowledgeGraphNode, type: 'KnowledgeGraph', name: '知识图谱', desc: '构建知识图谱' }
    ];

    // RAG应用节点 (4个)
    const ragNodes = [
      { nodeClass: RAGQueryNode, type: 'RAGQuery', name: 'RAG查询', desc: '执行RAG查询' },
      { nodeClass: KnowledgeBaseNode, type: 'KnowledgeBase', name: '知识库', desc: '管理知识库' },
      { nodeClass: DocumentIndexNode, type: 'DocumentIndex', name: '文档索引', desc: '创建文档索引' },
      { nodeClass: SemanticSearchNode, type: 'SemanticSearch', name: '语义搜索', desc: '执行语义搜索' }
    ];

    // 监控服务节点 (2个)
    const monitoringNodes = [
      { nodeClass: SystemMonitoringNode, type: 'SystemMonitoring', name: '系统监控', desc: '监控系统状态' },
      { nodeClass: PerformanceAnalysisNode, type: 'PerformanceAnalysis', name: '性能分析', desc: '分析系统性能' }
    ];

    // 通知服务节点 (1个)
    const notificationNodes = [
      { nodeClass: EmailNotificationNode, type: 'EmailNotification', name: '邮件通知', desc: '发送邮件通知' }
    ];

    // 注册所有专业节点
    const allProfessionalNodes = [
      ...blockchainNodes.map(node => ({ ...node, category: '区块链', color: '#FF9800' })),
      ...learningNodes.map(node => ({ ...node, category: '学习记录', color: '#3F51B5' })),
      ...ragNodes.map(node => ({ ...node, category: 'RAG应用', color: '#009688' })),
      ...monitoringNodes.map(node => ({ ...node, category: '监控服务', color: '#607D8B' })),
      ...notificationNodes.map(node => ({ ...node, category: '通知服务', color: '#795548' }))
    ];

    for (const { nodeClass, type, name, desc, category, color } of allProfessionalNodes) {
      try {
        this.nodeRegistry.registerNode(
          type,
          nodeClass,
          category,
          desc,
          'extension',
          color
        );
        this.registrationStats.registered++;
        console.log(`✓ 注册成功: ${name} (${type})`);
      } catch (error) {
        this.registrationStats.failed++;
        console.error(`✗ 注册失败: ${name} (${type})`, error);
      }
    }

    console.log('其他专业节点注册完成 - 13个节点');
  }

  /**
   * 获取注册统计信息
   */
  public getRegistrationStats(): any {
    return {
      ...this.registrationStats,
      successRate: this.registrationStats.total > 0
        ? (this.registrationStats.registered / this.registrationStats.total * 100).toFixed(2) + '%'
        : '0%'
    };
  }

  /**
   * 获取所有已注册的节点类型
   */
  public getAllRegisteredNodeTypes(): string[] {
    return [
      // 批次34扩展节点 (9个)
      'PaymentGateway', 'Subscription', 'InAppPurchase', 'WalletSystem',
      'TransactionHistory', 'PaymentAnalytics', 'GoogleServices',
      'FacebookIntegration', 'TwitterIntegration',

      // UI系统节点 (3个)
      'CreateUIElement', 'UILayout', 'UIEventHandler',

      // 动作捕捉节点 (3个)
      'FaceDetection', 'PoseDetection', 'VirtualInteraction',

      // 空间信息节点 (3个)
      'GISAnalysis', 'GeospatialVisualization', 'LocationServices',

      // 粒子系统节点 (2个)
      'ParticleEmitter', 'ParticleEffect',

      // 其他专业节点 (13个)
      // 区块链节点
      'WalletConnect', 'SmartContract', 'Transaction',
      // 学习记录节点
      'LearningRecord', 'LearningPath', 'KnowledgeGraph',
      // RAG应用节点
      'RAGQuery', 'KnowledgeBase', 'DocumentIndex', 'SemanticSearch',
      // 监控服务节点
      'SystemMonitoring', 'PerformanceAnalysis',
      // 通知服务节点
      'EmailNotification'
    ];
  }

  /**
   * 检查节点是否已注册
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
    this.registrationStats.registered = 0;
    this.registrationStats.failed = 0;
  }

  /**
   * 获取节点分类信息
   */
  public getNodeCategories(): any {
    return {
      '批次34扩展': {
        count: 9,
        description: '支付系统和第三方集成节点',
        color: '#9C27B0'
      },
      'UI系统': {
        count: 3,
        description: 'UI元素创建和管理节点',
        color: '#2196F3'
      },
      '动作捕捉': {
        count: 3,
        description: '面部和姿态检测节点',
        color: '#FF5722'
      },
      '空间信息': {
        count: 3,
        description: 'GIS和地理空间分析节点',
        color: '#4CAF50'
      },
      '粒子系统': {
        count: 2,
        description: '粒子效果创建和管理节点',
        color: '#E91E63'
      },
      '区块链': {
        count: 3,
        description: '区块链和加密货币节点',
        color: '#FF9800'
      },
      '学习记录': {
        count: 3,
        description: '学习管理和知识图谱节点',
        color: '#3F51B5'
      },
      'RAG应用': {
        count: 4,
        description: '检索增强生成应用节点',
        color: '#009688'
      },
      '监控服务': {
        count: 2,
        description: '系统监控和性能分析节点',
        color: '#607D8B'
      },
      '通知服务': {
        count: 1,
        description: '通知和消息发送节点',
        color: '#795548'
      }
    };
  }
}

// 导出单例实例
export const professionalExtensionNodesRegistry = ProfessionalExtensionNodesRegistry.getInstance();
